"""
数据标注API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.schemas.annotation import (
    AnnotationTaskCreate, AnnotationTaskUpdate, AnnotationTaskResponse, AnnotationTaskList,
    AnnotationCreate, AnnotationUpdate, AnnotationResponse, AnnotationList
)
from app.models.user import User
from app.services.annotation_service import AnnotationService

router = APIRouter()


# 标注任务相关端点
@router.get("/tasks", response_model=AnnotationTaskList)
async def read_annotation_tasks(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = Query(None, description="状态筛选"),
    assignee_id: Optional[str] = Query(None, description="分配给用户ID"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取标注任务列表
    """
    annotation_service = AnnotationService(db)
    
    filters = {}
    if status_filter:
        filters['status'] = status_filter
    if assignee_id:
        filters['assignee_id'] = assignee_id
    elif current_user.role not in ['admin', 'reviewer']:
        # 非管理员和审核员只能看到自己的任务
        filters['assignee_id'] = current_user.id
    
    tasks = annotation_service.get_tasks(
        skip=skip, 
        limit=limit, 
        filters=filters
    )
    total = annotation_service.count_tasks(filters=filters)
    
    return {
        "items": tasks,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/tasks", response_model=AnnotationTaskResponse)
async def create_annotation_task(
    *,
    db: Session = Depends(get_db),
    task_in: AnnotationTaskCreate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    创建标注任务
    """
    # 只有管理员和审核员可以创建任务
    if current_user.role not in ['admin', 'reviewer']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限创建标注任务"
        )
    
    annotation_service = AnnotationService(db)
    task = annotation_service.create_task(task_in, creator_id=current_user.id)
    return task


@router.get("/tasks/{task_id}", response_model=AnnotationTaskResponse)
async def read_annotation_task(
    task_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    根据ID获取标注任务详情
    """
    annotation_service = AnnotationService(db)
    task = annotation_service.get_task(task_id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标注任务不存在"
        )
    
    # 检查权限：只有任务分配者、创建者或管理员可以查看
    if (task.assignee_id != current_user.id and 
        task.creator_id != current_user.id and 
        current_user.role not in ['admin', 'reviewer']):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看此标注任务"
        )
    
    return task


@router.put("/tasks/{task_id}", response_model=AnnotationTaskResponse)
async def update_annotation_task(
    *,
    db: Session = Depends(get_db),
    task_id: str,
    task_in: AnnotationTaskUpdate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    更新标注任务
    """
    annotation_service = AnnotationService(db)
    task = annotation_service.get_task(task_id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标注任务不存在"
        )
    
    # 检查权限：只有创建者或管理员可以编辑
    if task.creator_id != current_user.id and current_user.role not in ['admin', 'reviewer']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限编辑此标注任务"
        )
    
    task = annotation_service.update_task(task, task_in)
    return task


@router.delete("/tasks/{task_id}")
async def delete_annotation_task(
    task_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    删除标注任务
    """
    annotation_service = AnnotationService(db)
    task = annotation_service.get_task(task_id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标注任务不存在"
        )
    
    # 检查权限：只有创建者或管理员可以删除
    if task.creator_id != current_user.id and current_user.role not in ['admin']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此标注任务"
        )
    
    annotation_service.remove_task(task_id)
    return {"message": "标注任务删除成功"}


# 标注记录相关端点
@router.get("/", response_model=AnnotationList)
async def read_annotations(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    task_id: Optional[str] = Query(None, description="任务ID筛选"),
    question_id: Optional[str] = Query(None, description="题目ID筛选"),
    annotator_id: Optional[str] = Query(None, description="标注者ID筛选"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取标注记录列表
    """
    annotation_service = AnnotationService(db)
    
    filters = {}
    if task_id:
        filters['task_id'] = task_id
    if question_id:
        filters['question_id'] = question_id
    if annotator_id:
        filters['annotator_id'] = annotator_id
    elif current_user.role not in ['admin', 'reviewer']:
        # 非管理员和审核员只能看到自己的标注
        filters['annotator_id'] = current_user.id
    
    annotations = annotation_service.get_annotations(
        skip=skip, 
        limit=limit, 
        filters=filters
    )
    total = annotation_service.count_annotations(filters=filters)
    
    return {
        "items": annotations,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/", response_model=AnnotationResponse)
async def create_annotation(
    *,
    db: Session = Depends(get_db),
    annotation_in: AnnotationCreate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    创建标注记录
    """
    annotation_service = AnnotationService(db)
    
    # 检查任务是否存在且用户有权限
    if annotation_in.task_id:
        task = annotation_service.get_task(annotation_in.task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="标注任务不存在"
            )
        
        if (task.assignee_id != current_user.id and 
            current_user.role not in ['admin', 'reviewer']):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限在此任务中进行标注"
            )
    
    annotation = annotation_service.create_annotation(annotation_in, annotator_id=current_user.id)
    return annotation


@router.get("/{annotation_id}", response_model=AnnotationResponse)
async def read_annotation(
    annotation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    根据ID获取标注记录详情
    """
    annotation_service = AnnotationService(db)
    annotation = annotation_service.get_annotation(annotation_id)
    
    if not annotation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标注记录不存在"
        )
    
    # 检查权限：只有标注者或管理员可以查看
    if (annotation.annotator_id != current_user.id and 
        current_user.role not in ['admin', 'reviewer']):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看此标注记录"
        )
    
    return annotation


@router.put("/{annotation_id}", response_model=AnnotationResponse)
async def update_annotation(
    *,
    db: Session = Depends(get_db),
    annotation_id: str,
    annotation_in: AnnotationUpdate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    更新标注记录
    """
    annotation_service = AnnotationService(db)
    annotation = annotation_service.get_annotation(annotation_id)
    
    if not annotation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标注记录不存在"
        )
    
    # 检查权限：只有标注者或管理员可以编辑
    if (annotation.annotator_id != current_user.id and 
        current_user.role not in ['admin', 'reviewer']):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限编辑此标注记录"
        )
    
    annotation = annotation_service.update_annotation(annotation, annotation_in)
    return annotation


@router.delete("/{annotation_id}")
async def delete_annotation(
    annotation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    删除标注记录
    """
    annotation_service = AnnotationService(db)
    annotation = annotation_service.get_annotation(annotation_id)
    
    if not annotation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标注记录不存在"
        )
    
    # 检查权限：只有标注者或管理员可以删除
    if (annotation.annotator_id != current_user.id and 
        current_user.role not in ['admin']):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此标注记录"
        )
    
    annotation_service.remove_annotation(annotation_id)
    return {"message": "标注记录删除成功"}


@router.post("/{annotation_id}/review")
async def review_annotation(
    annotation_id: str,
    review_status: str,
    review_comment: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    审核标注记录
    """
    # 只有审核员和管理员可以审核
    if current_user.role not in ['admin', 'reviewer']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限审核标注记录"
        )
    
    annotation_service = AnnotationService(db)
    annotation = annotation_service.get_annotation(annotation_id)
    
    if not annotation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标注记录不存在"
        )
    
    annotation_service.review_annotation(
        annotation_id=annotation_id,
        reviewer_id=current_user.id,
        status=review_status,
        comment=review_comment
    )
    
    return {"message": "标注审核完成"}
