# FastAPI 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# 数据库相关
sqlalchemy==2.0.23
alembic==1.13.0
psycopg2-binary==2.9.9
asyncpg==0.29.0

# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 缓存和会话
redis==5.0.1
aioredis==2.0.1

# HTTP 客户端
httpx==0.25.2
aiofiles==23.2.1

# 日志和监控
structlog==23.2.0
python-json-logger==2.0.7

# 异步任务
celery==5.3.4
flower==2.0.1

# 工具库
python-dotenv==1.0.0
click==8.1.7
typer==0.9.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2
factory-boy==3.3.0

# 代码质量
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8

# 性能分析
py-spy==0.3.14
memory-profiler==0.61.0

# 数据处理
pandas==2.1.3
numpy==1.25.2
openpyxl==3.1.2

# 图像处理（如果需要）
Pillow==10.1.0

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 配置管理
dynaconf==3.2.4

# API文档增强
fastapi-users==12.1.2
fastapi-pagination==0.12.13

# 中间件
slowapi==0.1.9
fastapi-limiter==0.1.5

# 邮件发送
fastapi-mail==1.4.1

# 文件处理
python-magic==0.4.27

# 加密
cryptography==41.0.7

# 环境变量
python-decouple==3.8
