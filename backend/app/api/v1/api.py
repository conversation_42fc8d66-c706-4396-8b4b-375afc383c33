"""
API v1 路由汇总
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    users,
    questions,
    knowledge_points,
    annotation,
    admin
)

api_router = APIRouter()

# 认证相关路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["认证"]
)

# 用户管理路由
api_router.include_router(
    users.router,
    prefix="/users",
    tags=["用户管理"]
)

# 题目管理路由
api_router.include_router(
    questions.router,
    prefix="/questions",
    tags=["题目管理"]
)

# 知识点管理路由
api_router.include_router(
    knowledge_points.router,
    prefix="/knowledge-points",
    tags=["知识点管理"]
)

# 标注相关路由
api_router.include_router(
    annotation.router,
    prefix="/annotation",
    tags=["数据标注"]
)

# 管理员路由
api_router.include_router(
    admin.router,
    prefix="/admin",
    tags=["系统管理"]
)
