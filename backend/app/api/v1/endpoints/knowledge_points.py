"""
知识点管理API端点
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.schemas.knowledge import KnowledgePointCreate, KnowledgePointUpdate, KnowledgePointResponse, KnowledgePointList
from app.models.user import User
from app.services.knowledge_service import KnowledgeService

router = APIRouter()


@router.get("/", response_model=KnowledgePointList)
async def read_knowledge_points(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="搜索关键词"),
    subject: Optional[str] = Query(None, description="学科筛选"),
    parent_id: Optional[str] = Query(None, description="父知识点ID"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取知识点列表
    """
    knowledge_service = KnowledgeService(db)
    
    filters = {}
    if subject:
        filters['subject'] = subject
    if parent_id:
        filters['parent_id'] = parent_id
    
    knowledge_points = knowledge_service.get_multi(
        skip=skip, 
        limit=limit, 
        search=search,
        filters=filters
    )
    total = knowledge_service.count(search=search, filters=filters)
    
    return {
        "items": knowledge_points,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.get("/tree")
async def get_knowledge_tree(
    db: Session = Depends(get_db),
    subject: Optional[str] = Query(None, description="学科筛选"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取知识点树形结构
    """
    knowledge_service = KnowledgeService(db)
    tree = knowledge_service.get_tree(subject=subject)
    return {"tree": tree}


@router.post("/", response_model=KnowledgePointResponse)
async def create_knowledge_point(
    *,
    db: Session = Depends(get_db),
    kp_in: KnowledgePointCreate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    创建新知识点
    """
    knowledge_service = KnowledgeService(db)
    
    # 检查父知识点是否存在
    if kp_in.parent_id:
        parent = knowledge_service.get(kp_in.parent_id)
        if not parent:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="父知识点不存在"
            )
    
    knowledge_point = knowledge_service.create(kp_in, creator_id=current_user.id)
    return knowledge_point


@router.get("/{kp_id}", response_model=KnowledgePointResponse)
async def read_knowledge_point(
    kp_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    根据ID获取知识点详情
    """
    knowledge_service = KnowledgeService(db)
    knowledge_point = knowledge_service.get(kp_id)
    
    if not knowledge_point:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识点不存在"
        )
    
    return knowledge_point


@router.put("/{kp_id}", response_model=KnowledgePointResponse)
async def update_knowledge_point(
    *,
    db: Session = Depends(get_db),
    kp_id: str,
    kp_in: KnowledgePointUpdate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    更新知识点信息
    """
    knowledge_service = KnowledgeService(db)
    knowledge_point = knowledge_service.get(kp_id)
    
    if not knowledge_point:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识点不存在"
        )
    
    # 检查权限：只有创建者或管理员可以编辑
    if knowledge_point.creator_id != current_user.id and current_user.role != 'admin':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限编辑此知识点"
        )
    
    # 检查父知识点是否存在
    if kp_in.parent_id and kp_in.parent_id != knowledge_point.parent_id:
        parent = knowledge_service.get(kp_in.parent_id)
        if not parent:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="父知识点不存在"
            )
        
        # 检查是否会形成循环
        if knowledge_service.would_create_cycle(kp_id, kp_in.parent_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能设置为子知识点的父节点，会形成循环"
            )
    
    knowledge_point = knowledge_service.update(knowledge_point, kp_in)
    return knowledge_point


@router.delete("/{kp_id}")
async def delete_knowledge_point(
    kp_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    删除知识点
    """
    knowledge_service = KnowledgeService(db)
    knowledge_point = knowledge_service.get(kp_id)
    
    if not knowledge_point:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识点不存在"
        )
    
    # 检查权限：只有创建者或管理员可以删除
    if knowledge_point.creator_id != current_user.id and current_user.role != 'admin':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此知识点"
        )
    
    # 检查是否有子知识点
    if knowledge_service.has_children(kp_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除有子知识点的知识点"
        )
    
    # 检查是否有关联的题目
    if knowledge_service.has_linked_questions(kp_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除有关联题目的知识点"
        )
    
    knowledge_service.remove(kp_id)
    return {"message": "知识点删除成功"}


@router.get("/{kp_id}/children")
async def get_knowledge_point_children(
    kp_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取知识点的子知识点
    """
    knowledge_service = KnowledgeService(db)
    knowledge_point = knowledge_service.get(kp_id)
    
    if not knowledge_point:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识点不存在"
        )
    
    children = knowledge_service.get_children(kp_id)
    return {"children": children}


@router.get("/{kp_id}/prerequisites")
async def get_knowledge_point_prerequisites(
    kp_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取知识点的先修知识点
    """
    knowledge_service = KnowledgeService(db)
    knowledge_point = knowledge_service.get(kp_id)
    
    if not knowledge_point:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识点不存在"
        )
    
    prerequisites = knowledge_service.get_prerequisites(kp_id)
    return {"prerequisites": prerequisites}


@router.post("/{kp_id}/prerequisites/{prereq_id}")
async def add_prerequisite(
    kp_id: str,
    prereq_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    添加先修关系
    """
    knowledge_service = KnowledgeService(db)
    
    # 检查知识点是否存在
    kp = knowledge_service.get(kp_id)
    prereq = knowledge_service.get(prereq_id)
    
    if not kp or not prereq:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识点不存在"
        )
    
    # 检查是否会形成循环
    if knowledge_service.would_create_prerequisite_cycle(kp_id, prereq_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能添加此先修关系，会形成循环"
        )
    
    knowledge_service.add_prerequisite(kp_id, prereq_id, current_user.id)
    return {"message": "先修关系添加成功"}


@router.delete("/{kp_id}/prerequisites/{prereq_id}")
async def remove_prerequisite(
    kp_id: str,
    prereq_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    移除先修关系
    """
    knowledge_service = KnowledgeService(db)
    
    # 检查知识点是否存在
    kp = knowledge_service.get(kp_id)
    if not kp:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识点不存在"
        )
    
    knowledge_service.remove_prerequisite(kp_id, prereq_id)
    return {"message": "先修关系移除成功"}
