"""
标注工作流模块数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

from sqlalchemy import (
    Column, String, <PERSON>ole<PERSON>, Integer, SmallInteger, Text, 
    Index, CheckConstraint, ForeignKey, BigInteger, DateTime
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, UserTrackingMixin


class TaskStatus(int, Enum):
    """任务状态枚举"""
    PENDING = 0      # 待处理
    IN_PROGRESS = 1  # 进行中
    COMPLETED = 2    # 已完成
    REVIEWED = 3     # 已审核
    REJECTED = 4     # 已拒绝
    CANCELLED = 5    # 已取消


class TaskType(int, Enum):
    """任务类型枚举"""
    QUESTION_KP_MAPPING = 0    # 题目-知识点映射
    PREREQUISITE_RELATION = 1  # 先修关系标注
    QUESTION_RELATION = 2      # 题目关系标注
    KNOWLEDGE_SPACE = 3        # 知识空间构建
    QUALITY_CHECK = 4          # 质量检查


class LogAction(int, Enum):
    """日志操作类型枚举"""
    CREATE = 0      # 创建
    UPDATE = 1      # 更新
    DELETE = 2      # 删除
    ASSIGN = 3      # 分配
    SUBMIT = 4      # 提交
    REVIEW = 5      # 审核
    APPROVE = 6     # 批准
    REJECT = 7      # 拒绝
    CANCEL = 8      # 取消


class AnnotationTask(Base, TimestampMixin, UserTrackingMixin):
    """标注任务表"""
    
    __tablename__ = "annotation_tasks"
    
    # 主键
    task_id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="任务ID"
    )
    
    # 任务基本信息
    title = Column(
        Text,
        nullable=False,
        comment="任务标题"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="任务描述"
    )
    
    task_type = Column(
        SmallInteger,
        nullable=False,
        comment="任务类型"
    )
    
    # 任务状态
    status = Column(
        SmallInteger,
        nullable=False,
        default=TaskStatus.PENDING.value,
        comment="任务状态"
    )
    
    priority = Column(
        SmallInteger,
        nullable=False,
        default=3,
        comment="优先级(1-5)"
    )
    
    # 分配信息
    assigned_to = Column(
        BigInteger,
        ForeignKey("users.user_id"),
        nullable=True,
        comment="分配给用户ID"
    )
    
    reviewer_id = Column(
        BigInteger,
        ForeignKey("users.user_id"),
        nullable=True,
        comment="审核员ID"
    )
    
    # 时间信息
    due_date = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="截止时间"
    )
    
    started_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="开始时间"
    )
    
    completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="完成时间"
    )
    
    reviewed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="审核时间"
    )
    
    # 任务数据
    task_data = Column(
        JSONB,
        nullable=True,
        comment="任务数据(JSON格式)"
    )
    
    result_data = Column(
        JSONB,
        nullable=True,
        comment="结果数据(JSON格式)"
    )
    
    # 进度信息
    progress = Column(
        SmallInteger,
        nullable=False,
        default=0,
        comment="完成进度(0-100)"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            task_type.in_([t.value for t in TaskType]),
            name="ck_annotation_tasks_task_type"
        ),
        CheckConstraint(
            status.in_([s.value for s in TaskStatus]),
            name="ck_annotation_tasks_status"
        ),
        CheckConstraint(
            "priority >= 1 AND priority <= 5",
            name="ck_annotation_tasks_priority"
        ),
        CheckConstraint(
            "progress >= 0 AND progress <= 100",
            name="ck_annotation_tasks_progress"
        ),
        Index("idx_annotation_tasks_task_type", task_type),
        Index("idx_annotation_tasks_status", status),
        Index("idx_annotation_tasks_assigned_to", assigned_to),
        Index("idx_annotation_tasks_reviewer_id", reviewer_id),
        Index("idx_annotation_tasks_due_date", due_date),
        Index("idx_annotation_tasks_priority", priority),
        Index("idx_annotation_tasks_created_by", created_by),
        {"comment": "标注任务表"}
    )
    
    # 关系
    assignee = relationship("User", foreign_keys=[assigned_to])
    reviewer = relationship("User", foreign_keys=[reviewer_id])
    logs = relationship("AnnotationLog", back_populates="task", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<AnnotationTask(id={self.task_id}, title='{self.title}', status={self.status})>"


class AnnotationLog(Base, TimestampMixin):
    """标注操作日志表"""
    
    __tablename__ = "annotation_logs"
    
    # 主键
    log_id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="日志ID"
    )
    
    # 外键
    task_id = Column(
        BigInteger,
        ForeignKey("annotation_tasks.task_id"),
        nullable=True,
        comment="任务ID"
    )
    
    user_id = Column(
        BigInteger,
        ForeignKey("users.user_id"),
        nullable=False,
        comment="操作用户ID"
    )
    
    # 操作信息
    action = Column(
        SmallInteger,
        nullable=False,
        comment="操作类型"
    )
    
    description = Column(
        Text,
        nullable=False,
        comment="操作描述"
    )
    
    # 操作数据
    old_data = Column(
        JSONB,
        nullable=True,
        comment="操作前数据"
    )
    
    new_data = Column(
        JSONB,
        nullable=True,
        comment="操作后数据"
    )
    
    # 元数据
    ip_address = Column(
        String(45),
        nullable=True,
        comment="IP地址"
    )
    
    user_agent = Column(
        Text,
        nullable=True,
        comment="用户代理"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            action.in_([a.value for a in LogAction]),
            name="ck_annotation_logs_action"
        ),
        Index("idx_annotation_logs_task_id", task_id),
        Index("idx_annotation_logs_user_id", user_id),
        Index("idx_annotation_logs_action", action),
        Index("idx_annotation_logs_created_at", "created_at"),
        {"comment": "标注操作日志表"}
    )
    
    # 关系
    task = relationship("AnnotationTask", back_populates="logs")
    user = relationship("User")
    
    def __repr__(self) -> str:
        return f"<AnnotationLog(id={self.log_id}, task_id={self.task_id}, action={self.action})>"
